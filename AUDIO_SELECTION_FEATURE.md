# 音频选择功能实现说明

本文档说明如何在 AudioListWidget 中实现点击选中音频项并添加边框颜色的功能。

## 🎯 功能特点

### 选中状态视觉反馈
- **边框颜色变化**：选中时边框变为青绿色 (`#30E6B8`)，边框宽度从 1px 增加到 2px
- **文字颜色变化**：选中时标题文字变为青绿色，并加粗显示
- **选中指示器**：在音频项右上角显示小圆形勾选图标
- **动画效果**：所有状态变化都有 200ms 的平滑过渡动画

### 交互逻辑
- **单选模式**：非管理模式下，点击音频项进行选中/取消选中
- **切换选择**：再次点击已选中的项可以取消选中
- **管理模式兼容**：管理模式下保持原有的多选删除功能
- **状态隔离**：选中状态与管理模式的选择状态完全独立

## 🔧 技术实现

### 状态管理
```dart
class _AudioListWidgetState extends State<AudioListWidget> {
  String? _currentSelectedItemId; // 当前选中的音频项ID（非管理模式）
  // ... 其他状态
}
```

### 选中逻辑
```dart
void _selectAudioItem(AudioItem audioItem) {
  setState(() {
    // 如果点击的是当前选中的项，则取消选中
    if (_currentSelectedItemId == audioItem.id) {
      _currentSelectedItemId = null;
    } else {
      _currentSelectedItemId = audioItem.id;
    }
  });
  
  // 调用回调函数
  widget.onAudioItemTap?.call(audioItem);
}
```

### 视觉样式
```dart
// 边框样式
border: Border.all(
  color: isCurrentSelected 
      ? const Color(0xFF30E6B8) // 选中时的青绿色边框
      : const Color(0xFF565656), // 默认边框颜色
  width: isCurrentSelected ? 2 : 1, // 选中时边框更粗
),

// 文字样式
style: TextStyle(
  color: isCurrentSelected 
      ? const Color(0xFF30E6B8) // 选中时文字颜色
      : Colors.white, // 默认文字颜色
  fontSize: 12,
  fontWeight: isCurrentSelected 
      ? FontWeight.w600 // 选中时字体加粗
      : FontWeight.normal,
),
```

## 🚀 使用方法

### 基本用法
```dart
AudioListWidget(
  audioItems: audioItems,
  onAudioItemTap: (audioItem) {
    // 处理音频项选中事件
    print('选中音频: ${audioItem.title}');
    // 可以在这里更新父组件的状态
    setState(() {
      selectedAudio = audioItem;
    });
  },
  // ... 其他参数
)
```

### 完整示例
```dart
class MyAudioPage extends StatefulWidget {
  @override
  State<MyAudioPage> createState() => _MyAudioPageState();
}

class _MyAudioPageState extends State<MyAudioPage> {
  AudioItem? selectedAudioItem;
  List<AudioItem> audioItems = [...];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 显示当前选中的音频
          if (selectedAudioItem != null)
            Container(
              padding: EdgeInsets.all(16),
              child: Text(
                '当前选中: ${selectedAudioItem!.title}',
                style: TextStyle(
                  color: Color(0xFF30E6B8),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          
          // 音频列表
          AudioListWidget(
            audioItems: audioItems,
            onAudioItemTap: (audioItem) {
              setState(() {
                selectedAudioItem = audioItem;
              });
              
              // 显示选择反馈
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已选择: ${audioItem.title}')),
              );
            },
          ),
        ],
      ),
    );
  }
}
```

## 🎨 自定义样式

### 颜色配置
可以通过修改以下颜色值来自定义选中状态的外观：

```dart
// 选中边框颜色
const Color(0xFF30E6B8) // 青绿色

// 选中文字颜色  
const Color(0xFF30E6B8) // 青绿色

// 选中指示器背景色
const Color(0xFF30E6B8) // 青绿色

// 默认边框颜色
const Color(0xFF565656) // 灰色

// 默认文字颜色
Colors.white // 白色
```

### 动画配置
```dart
// 状态变化动画时长
duration: const Duration(milliseconds: 200)

// 可以调整为其他时长，如：
duration: const Duration(milliseconds: 150) // 更快
duration: const Duration(milliseconds: 300) // 更慢
```

## 📱 用户体验

### 视觉层次
1. **未选中状态**：灰色边框，白色文字，无特殊标识
2. **选中状态**：青绿色边框（加粗），青绿色文字（加粗），右上角勾选图标
3. **管理模式**：保持原有的多选样式，与选中状态互不干扰

### 交互反馈
- **即时反馈**：点击后立即显示选中状态
- **状态切换**：支持选中/取消选中的切换
- **回调通知**：通过 `onAudioItemTap` 回调通知父组件
- **动画过渡**：所有状态变化都有平滑的动画效果

## 🔄 状态管理

### 内部状态
- `_currentSelectedItemId`: 当前选中的音频项ID
- `_isManagementMode`: 管理模式状态
- `_selectedItemIds`: 管理模式下的多选状态

### 状态隔离
- 选中状态（单选）与管理模式状态（多选）完全独立
- 进入/退出管理模式不会影响当前的选中状态
- 删除音频时会自动清理相关状态

## 🐛 注意事项

1. **状态同步**：确保在删除音频时清理选中状态
2. **回调处理**：`onAudioItemTap` 回调会在每次选中/取消选中时触发
3. **性能优化**：使用 `AnimatedContainer` 实现平滑的状态过渡
4. **可访问性**：选中状态有明确的视觉反馈，便于用户识别

## 📋 API 参考

### 回调函数
```dart
typedef OnAudioItemTap = void Function(AudioItem audioItem);
```

### 组件参数
- `onAudioItemTap`: 音频项点击回调，每次选中/取消选中时触发
- 其他参数保持不变，完全向后兼容

## 🎯 最佳实践

1. **状态管理**：在父组件中维护当前选中的音频项状态
2. **用户反馈**：使用 SnackBar 或其他方式给用户明确的选择反馈
3. **数据同步**：确保选中状态与实际的业务逻辑保持同步
4. **错误处理**：处理音频项被删除后的状态清理
