import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/audio/audio_item.dart';
import 'package:text_generation_video/app/repository/service/audio_img_to_video_service.dart';
import 'package:text_generation_video/app/widgets/audio/audio_recording_dialog.dart';
import 'package:text_generation_video/utils/toast_util.dart';

/// Service class for handling audio recording upload and processing
class AudioRecordingService {
  /// Upload recorded audio file and create AudioItem
  static Future<AudioItem?> uploadRecordedAudio({
    required String audioFilePath,
    required Duration duration,
    String? customTitle,
  }) async {
    try {
      // Show loading dialog
      SmartDialog.showLoading(msg: "上传音频中...");

      // Upload the audio file
      final uploadResult =
          await AudioImgToVideoService.UploadAudioFile(audioFilePath);

      SmartDialog.dismiss();

      if (uploadResult.status == Status.completed &&
          uploadResult.data != null) {
        // Create AudioItem with uploaded URL
        final audioItem = AudioItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: customTitle ?? _generateAudioTitle(duration),
          audioUrl: uploadResult.data!,
          duration: duration,
          createdAt: DateTime.now(),
        );

        // Clean up local file
        await _cleanupLocalFile(audioFilePath);

        ToastUtil.showToast("音频上传成功");
        return audioItem;
      } else {
        // Handle upload failure
        await _cleanupLocalFile(audioFilePath);
        ToastUtil.showToast(uploadResult.exception?.getMessage() ?? "音频上传失败");
        return null;
      }
    } catch (e) {
      SmartDialog.dismiss();
      await _cleanupLocalFile(audioFilePath);
      ToastUtil.showToast("音频上传失败: $e");
      debugPrint("Audio upload error: $e");
      return null;
    }
  }

  /// Generate a default title for the audio based on duration and timestamp
  static String _generateAudioTitle(Duration duration) {
    final now = DateTime.now();
    final timeString =
        "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}";
    final durationString =
        "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}";
    return "录音 $timeString ($durationString)";
  }

  /// Clean up the local audio file after upload
  static Future<void> _cleanupLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint("Cleaned up local audio file: $filePath");
      }
    } catch (e) {
      debugPrint("Error cleaning up local file: $e");
    }
  }

  /// Validate audio file before upload
  static Future<bool> validateAudioFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        ToastUtil.showToast("音频文件不存在");
        return false;
      }

      final fileSize = await file.length();
      const maxSizeBytes = 10 * 1024 * 1024; // 10MB limit

      if (fileSize > maxSizeBytes) {
        ToastUtil.showToast("音频文件过大，请录制较短的音频");
        return false;
      }

      return true;
    } catch (e) {
      debugPrint("Error validating audio file: $e");
      ToastUtil.showToast("音频文件验证失败");
      return false;
    }
  }

  /// Show recording dialog and handle the complete flow
  static Future<AudioItem?> showRecordingDialog(
    BuildContext context, {
    Duration maxDuration = const Duration(seconds: 15),
  }) async {
    AudioItem? result;

    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AudioRecordingDialog(
        maxDuration: maxDuration,
        onRecordingComplete: (audioFilePath, duration) async {
          Navigator.of(context).pop();

          // Validate the recorded file
          if (await validateAudioFile(audioFilePath)) {
            // Upload and process the audio
            result = await uploadRecordedAudio(
              audioFilePath: audioFilePath,
              duration: duration,
            );
          }
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
      ),
    );

    return result;
  }
}
