import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';

/// Callback function for when recording is completed
typedef OnRecordingComplete = void Function(
    String audioFilePath, Duration duration);

/// Audio recording dialog with countdown timer and recording controls
class AudioRecordingDialog extends StatefulWidget {
  const AudioRecordingDialog({
    super.key,
    this.maxDuration = const Duration(seconds: 15),
    this.onRecordingComplete,
    this.onCancel,
  });

  final Duration maxDuration;
  final OnRecordingComplete? onRecordingComplete;
  final VoidCallback? onCancel;

  @override
  State<AudioRecordingDialog> createState() => _AudioRecordingDialogState();
}

class _AudioRecordingDialogState extends State<AudioRecordingDialog>
    with TickerProviderStateMixin {
  final AudioRecorder _audioRecorder = AudioRecorder();

  bool _isRecording = false;
  bool _isPermissionGranted = false;
  bool _isInitializing = true;
  Duration _currentDuration = Duration.zero;
  Timer? _timer;
  String? _audioFilePath;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    _initializeAnimations();
    _checkPermissionAndInitialize();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _progressController = AnimationController(
      duration: widget.maxDuration,
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));
  }

  Future<void> _checkPermissionAndInitialize() async {
    final permission = await _audioRecorder.hasPermission();
    setState(() {
      _isPermissionGranted = permission;
      _isInitializing = false;
    });
  }

  Future<void> _startRecording() async {
    if (!_isPermissionGranted) return;

    try {
      // Get temporary directory for recording
      final tempDir = await getTemporaryDirectory();
      final fileName = 'audio_${DateTime.now().millisecondsSinceEpoch}.m4a';
      _audioFilePath = '${tempDir.path}/$fileName';

      // Start recording
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _audioFilePath!,
      );

      setState(() {
        _isRecording = true;
        _currentDuration = Duration.zero;
      });

      // Start animations
      _pulseController.repeat(reverse: true);
      _progressController.forward();

      // Start timer
      _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
        setState(() {
          _currentDuration = Duration(milliseconds: timer.tick * 100);
        });

        // Auto-stop when max duration is reached
        if (_currentDuration >= widget.maxDuration) {
          _stopRecording();
        }
      });
    } catch (e) {
      debugPrint('Error starting recording: $e');
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;

    try {
      await _audioRecorder.stop();
      _timer?.cancel();
      _pulseController.stop();
      _progressController.stop();

      setState(() {
        _isRecording = false;
      });

      // Call completion callback
      if (_audioFilePath != null && widget.onRecordingComplete != null) {
        widget.onRecordingComplete!(_audioFilePath!, _currentDuration);
      }
    } catch (e) {
      debugPrint('Error stopping recording: $e');
    }
  }

  void _cancelRecording() async {
    if (_isRecording) {
      await _audioRecorder.stop();
      _timer?.cancel();
      _pulseController.stop();
      _progressController.stop();

      // Delete the recorded file
      if (_audioFilePath != null) {
        final file = File(_audioFilePath!);
        if (await file.exists()) {
          await file.delete();
        }
      }
    }

    widget.onCancel?.call();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    _progressController.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF2D2C2F),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            if (_isInitializing) _buildInitializing(),
            if (!_isInitializing && !_isPermissionGranted)
              _buildPermissionDenied(),
            if (!_isInitializing && _isPermissionGranted)
              _buildRecordingInterface(),
            const SizedBox(height: 24),
            _buildControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return const Text(
      "录制音频",
      style: TextStyle(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildInitializing() {
    return const Column(
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF30E6B8)),
        ),
        SizedBox(height: 16),
        Text(
          "正在初始化...",
          style: TextStyle(
            color: Color(0xFF8A8D93),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionDenied() {
    return const Column(
      children: [
        Icon(
          Icons.mic_off,
          color: Color(0xFFFF4757),
          size: 48,
        ),
        SizedBox(height: 16),
        Text(
          "需要麦克风权限才能录制音频",
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        SizedBox(height: 8),
        Text(
          "请在设置中允许应用访问麦克风",
          style: TextStyle(
            color: Color(0xFF8A8D93),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildRecordingInterface() {
    final remainingSeconds =
        (widget.maxDuration.inSeconds - _currentDuration.inSeconds)
            .clamp(0, widget.maxDuration.inSeconds);

    return Column(
      children: [
        // Recording indicator with pulse animation
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _isRecording ? _pulseAnimation.value : 1.0,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: _isRecording
                      ? const Color(0xFFFF4757)
                      : const Color(0xFF30E6B8),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.mic,
                  color: Colors.white,
                  size: 48,
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 24),

        // Progress indicator
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return Column(
              children: [
                LinearProgressIndicator(
                  value: _progressAnimation.value,
                  backgroundColor: const Color(0xFF565656),
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(Color(0xFF30E6B8)),
                ),
                const SizedBox(height: 16),
                Text(
                  "${remainingSeconds}s",
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            );
          },
        ),

        const SizedBox(height: 16),

        // Recording status
        Text(
          _isRecording ? "正在录制..." : "点击开始录制",
          style: const TextStyle(
            color: Color(0xFF8A8D93),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildControls() {
    if (_isInitializing || !_isPermissionGranted) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: _cancelRecording,
            child: const Text(
              "取消",
              style: TextStyle(
                color: Color(0xFF8A8D93),
                fontSize: 16,
              ),
            ),
          ),
        ],
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Cancel button
        TextButton(
          onPressed: _cancelRecording,
          child: const Text(
            "取消",
            style: TextStyle(
              color: Color(0xFF8A8D93),
              fontSize: 16,
            ),
          ),
        ),

        // Record/Stop button
        ElevatedButton(
          onPressed: _isRecording ? _stopRecording : _startRecording,
          style: ElevatedButton.styleFrom(
            backgroundColor: _isRecording
                ? const Color(0xFFFF4757)
                : const Color(0xFF30E6B8),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            _isRecording ? "结束录制" : "开始录制",
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
