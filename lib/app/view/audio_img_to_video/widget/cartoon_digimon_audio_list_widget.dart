import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/audio/audio_list_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/dialog/audio_recording_bottom_sheet.dart';
import 'package:text_generation_video/app/widgets/audio/audio_list_widget.dart';
import 'package:text_generation_video/app/repository/service/audio_recording_service.dart';

class AudioListWithProviderWidget extends ConsumerWidget {
  const AudioListWithProviderWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听音频列表状态
    final audioListAsync = ref.watch(audioListProvider);

    return audioListAsync.when(
      data: (audioList) => _buildAudioList(context, ref, audioList),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载音频列表失败',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(audioListProvider.notifier).refresh();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建音频列表UI
  Widget _buildAudioList(
      BuildContext context, WidgetRef ref, List<AudioItem> audioItems) {
    return AudioListWidget(
      audioItems: audioItems,
      onDeleteAudioItems: (selectedItems) async {
        await ref.read(audioListProvider.notifier).removeAudios(selectedItems);
        debugPrint("删除 ${selectedItems.length} 个音频项");
      },
      onAudioItemTap: (audioItem) {
        // 处理音频项点击（播放音频、显示详情等）
        debugPrint("点击音频: ${audioItem.title}");
        // _showAudioDetails(context, audioItem);
        // 把audioItem 往其他provider set
      },
      onAddAudioTap: () async {
        await _handleAddAudio(context, ref);
      },
    );
  }

  /// 处理添加音频
  Future<void> _handleAddAudio(BuildContext context, WidgetRef ref) async {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      backgroundColor: Colors.transparent,
      builder: (context) => AudioRecordingBottomSheet(
        maxDuration: const Duration(seconds: 15),
        onRecordingComplete: (audioFilePath, duration) async {
          Navigator.of(context).pop();

          // 上传前验证文件
          if (await AudioRecordingService.validateAudioFile(audioFilePath)) {
            // 上传音频文件到服务器
            await AudioRecordingService.uploadRecordedAudio(
                audioFilePath: audioFilePath,
                duration: duration,
                onAudioAdded: (audioItem) {
                  ref.read(audioListProvider.notifier).refresh();
                  debugPrint("音频添加成功: ${audioItem.title}");
                });
          }
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// 显示音频详情
  void _showAudioDetails(BuildContext context, AudioItem audioItem) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(audioItem.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${audioItem.id}'),
            if (audioItem.duration != null)
              Text('时长: ${_formatDuration(audioItem.duration!)}'),
            if (audioItem.fileSize != null)
              Text('大小: ${_formatFileSize(audioItem.fileSize!)}'),
            if (audioItem.audioFormat != null)
              Text('格式: ${audioItem.audioFormat}'),
            if (audioItem.createdAt != null)
              Text('创建时间: ${_formatDateTime(audioItem.createdAt!)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 格式化时长
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
