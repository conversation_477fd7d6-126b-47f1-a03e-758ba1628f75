import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/audio/audio_list_provider.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/widget/cartoon_digimon_audio_list_widget.dart';
import 'package:text_generation_video/app/widgets/audio/audio_list_widget.dart';
import 'package:text_generation_video/app/repository/service/audio_recording_service.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class CartoonDigimonWidget extends ConsumerStatefulWidget {
  const CartoonDigimonWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CartoonDigimoWidgetState();
}

class _CartoonDigimoWidgetState extends ConsumerState<CartoonDigimonWidget> {
  Widget _buildUploadImg() {
    final currentModification = ref.watch(photoModificationCurrentProvider);

    return Container(
      margin: const EdgeInsets.fromLTRB(14, 14, 14, 0),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFF565656), width: 0.6),
      ),
      width: double.infinity,
      height: 450,
      padding: const EdgeInsets.all(6),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: const Color(0xFF191619),
              ),
              child: UploadStatusWidget(
                modification: currentModification,
                defaultChild: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    ref
                        .read(photoModificationCurrentProvider.notifier)
                        .selectImg();
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(modificationSelectIcon, width: 26),
                      const SizedBox(height: 9),
                      const Text(
                        "上传照片",
                        style:
                            TextStyle(fontSize: 14, color: Color(0xFF8A8D93)),
                      ),
                      const SizedBox(height: 4),
                      const SizedBox(
                        width: 200,
                        child: Text(
                          "仅支持mp4/mov/wmv/wav格式时长不超过2分钟",
                          style:
                              TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                uploadingChild: const Text(
                  "视频上传中\n请耐心等待",
                  style: TextStyle(fontSize: 16, color: Color(0xFFFFFFFF)),
                ),
              ),
            ),
          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "示例",
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
              const SizedBox(height: 6),
              _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg",
                title: "正脸自拍",
                isError: false,
              ),
              const SizedBox(height: 6),
              _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg",
                title: "侧脸",
                isError: true,
              ),
              const SizedBox(height: 6),
              _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg",
                title: "面部有干扰",
                isError: true,
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildSampleImg(
      {required String imgUrl, required String title, required bool isError}) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: CachedNetworkImage(
            imageUrl: imgUrl,
            width: 130,
            height: 130,
            fit: BoxFit.cover,
            errorWidget: (_, o, s) {
              return Container(
                width: 130,
                height: 130,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xFF454348),
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 25,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withAlpha(100),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          left: 0,
          bottom: 6,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    isError ? errorExamWhiteIcon : correctExamIcon,
                    width: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    title,
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }


  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: SingleChildScrollView(
            padding: EdgeInsets.only(
              top: 10,
              bottom: MediaQuery.paddingOf(context).bottom + 134,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUploadImg(),
                const AudioListWithProviderWidget(),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0x2518161A),
                  Color(0xFF18161A),
                  Color(0xFF18161A)
                ],
                // stops: [0.05, 0.06, 1],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const ConsumptionDisplayWidget(consumption: 20),
                GradientButton(
                  onPress: () {},
                  radius: 16,
                  shadow: false,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                  ),
                  child: const Text(
                    "生成视频",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF18161A),
                    ),
                  ),
                ),
                SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
