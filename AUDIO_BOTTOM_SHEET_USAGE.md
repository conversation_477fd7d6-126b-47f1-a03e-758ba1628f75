# 音频录制底部弹窗使用指南

本文档说明如何使用新的音频录制底部弹窗功能。

## 🎯 主要改进

- **更好的用户体验**：使用 `showModalBottomSheet` 替代传统对话框
- **现代化设计**：底部弹窗符合现代移动应用设计规范
- **拖拽指示器**：用户可以通过拖拽关闭弹窗
- **更大的操作区域**：底部弹窗提供更多空间展示录音界面
- **保持向后兼容**：原有的 `showRecordingDialog` 方法仍然可用

## 📱 界面特点

### 底部弹窗设计
- 圆角顶部设计
- 拖拽指示器
- 透明背景
- 自适应内容高度
- 支持安全区域

### 录音界面
- 大尺寸录音按钮（140x140）
- 脉冲动画效果
- 阴影效果增强视觉层次
- 进度条显示录音进度
- 倒计时显示剩余时间

### 按钮布局
- 取消按钮和录音按钮并排显示
- 录音按钮占用更多空间（flex: 2）
- 圆角按钮设计
- 状态颜色区分（绿色开始，红色停止）

## 🚀 使用方法

### 基本用法

```dart
import 'package:text_generation_video/app/repository/service/audio_recording_service.dart';

// 显示录音底部弹窗
AudioRecordingService.showRecordingBottomSheet(
  context,
  onAudioAdded: (audioItem) {
    print('录制完成: ${audioItem.title}');
  },
);
```

### 自定义录音时长

```dart
AudioRecordingService.showRecordingBottomSheet(
  context,
  maxDuration: const Duration(seconds: 30), // 30秒录音
  onAudioAdded: (audioItem) {
    // 处理录制完成的音频
  },
);
```

### 在FloatingActionButton中使用

```dart
FloatingActionButton(
  onPressed: () {
    AudioRecordingService.showRecordingBottomSheet(
      context,
      onAudioAdded: (audioItem) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('录制完成: ${audioItem.title}')),
        );
      },
    );
  },
  child: const Icon(Icons.mic),
)
```

## 🔄 迁移指南

### 从旧版对话框迁移

**旧版本（仍可用，但已弃用）：**
```dart
AudioRecordingService.showRecordingDialog(context);
```

**新版本（推荐）：**
```dart
AudioRecordingService.showRecordingBottomSheet(context);
```

### 参数保持一致

两个方法的参数完全相同：
- `context`: BuildContext
- `maxDuration`: Duration（可选，默认15秒）
- `onAudioAdded`: Function(AudioItem)?（可选）

## 🎨 自定义样式

### 颜色主题
- 主色调：`Color(0xFF30E6B8)` (青绿色)
- 录制状态：`Color(0xFFFF4757)` (红色)
- 背景色：`Color(0xFF2D2C2F)` (深灰色)
- 文字颜色：`Colors.white` 和 `Color(0xFF8A8D93)`

### 动画效果
- 脉冲动画：录制时按钮缩放动画
- 进度动画：线性进度条动画
- 时长：1秒脉冲周期

## 📋 完整示例

```dart
class AudioRecordingExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('音频录制示例')),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            AudioRecordingService.showRecordingBottomSheet(
              context,
              maxDuration: const Duration(seconds: 20),
              onAudioAdded: (audioItem) {
                // 录制成功回调
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('录制完成'),
                    content: Text('音频: ${audioItem.title}'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('确定'),
                      ),
                    ],
                  ),
                );
              },
            );
          },
          child: Text('开始录音'),
        ),
      ),
    );
  }
}
```

## 🔧 技术实现

### 核心组件
- `AudioRecordingBottomSheet`: 底部弹窗组件
- `AudioRecordingService`: 录音服务类
- `showModalBottomSheet`: Flutter底部弹窗API

### 关键特性
- `isScrollControlled: true`: 允许弹窗占用更多屏幕空间
- `backgroundColor: Colors.transparent`: 透明背景支持自定义圆角
- 自动权限检测和处理
- 录音文件自动上传和清理
- 错误处理和用户反馈

## 📱 兼容性

- **Flutter版本**: 支持Flutter 3.0+
- **平台支持**: iOS和Android
- **权限要求**: 麦克风权限
- **向后兼容**: 保持与旧版API的兼容性

## 🎯 最佳实践

1. **用户体验**：在用户点击录音按钮前，确保已获得麦克风权限
2. **错误处理**：在 `onAudioAdded` 回调中处理上传成功/失败的情况
3. **界面反馈**：使用SnackBar或其他方式给用户明确的反馈
4. **录音时长**：根据应用场景设置合适的最大录音时长
5. **网络状态**：在网络不佳时给用户适当提示

## 🐛 常见问题

**Q: 底部弹窗无法显示？**
A: 确保传入的context是有效的，并且当前页面支持弹窗显示。

**Q: 录音权限被拒绝？**
A: 组件会自动检测权限状态并显示相应提示，引导用户到设置中开启权限。

**Q: 录音文件上传失败？**
A: 检查网络连接，确保服务器端点可用。组件会自动重试并显示错误信息。

**Q: 如何自定义弹窗样式？**
A: 可以修改 `AudioRecordingBottomSheet` 组件中的颜色和样式常量。
